Metadata-Version: 2.4
Name: agent
Version: 0.0.1
Summary: Starter template for making a new agent LangGraph.
Author-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.12
Requires-Dist: python-dotenv>=1.0.1
Requires-Dist: supabase>=2.15.0
Requires-Dist: fastapi>=0.115.12
Requires-Dist: celery>=5.5.1
Requires-Dist: redis>=5.2.1
Requires-Dist: asyncpg>=0.30.0
Requires-Dist: langchain>=0.3.23
Requires-Dist: langchain_community>=0.3.21
Requires-Dist: langchain-openai>=0.3.12
Requires-Dist: langchain-aws>=0.2.18
Requires-Dist: httpx>=0.27.0
Requires-Dist: pydantic-settings>=2.8.1
Requires-Dist: aiofiles>=24.1.0
Requires-Dist: pandas>=2.1.0
Requires-Dist: uvicorn>=0.29.0
Requires-Dist: flower>=2.0.1
Requires-Dist: psycopg2-binary>=2.9.10
Requires-Dist: eventlet>=0.39.1
Requires-Dist: stripe>=12.1.0
Requires-Dist: firecrawl-py==2.5.4
Requires-Dist: langgraph-cli>=0.3.1
Requires-Dist: fastapi-limiter>=0.1.6
Requires-Dist: dotenv>=0.9.9
Provides-Extra: dev
Requires-Dist: mypy>=1.11.1; extra == "dev"
Requires-Dist: ruff>=0.6.1; extra == "dev"
