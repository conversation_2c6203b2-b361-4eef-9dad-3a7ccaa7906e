[project]
name = "agent"
version = "0.0.1"
description = "Starter template for making a new agent LangGraph."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.12"
dependencies = [
    "python-dotenv>=1.0.1",
    "supabase>=2.15.0",
    "fastapi>=0.115.12",
    "celery>=5.5.1",
    "redis>=5.2.1",
    "asyncpg>=0.30.0",
    "langchain>=0.3.23",
    "langchain_community>=0.3.21",
    "langchain-openai>=0.3.12",
    "langchain-aws>=0.2.18",
    "httpx>=0.27.0",
    "pydantic-settings>=2.8.1",
    "aiofiles>=24.1.0",
    "pandas>=2.1.0",
    "uvicorn>=0.29.0",
    "flower>=2.0.1",
    "psycopg2-binary>=2.9.10",
    "eventlet>=0.39.1",
    "stripe>=12.1.0",
    "firecrawl-py==2.5.4",
    "langgraph-cli>=0.3.1",
    "fastapi-limiter>=0.1.6",
    "dotenv>=0.9.9",
]


[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["langgraph.templates.agent", "agent"]
[tool.setuptools.package-dir]
"langgraph.templates.agent" = "src/agent"
"agent" = "src/agent"


[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
[tool.ruff.lint.pydocstyle]
convention = "google"

[dependency-groups]
dev = [
    "langgraph-cli[inmem]>=0.3.3",
]
